# 报价关注与通知功能设计方案

## 1. 功能简介

为增强平台内用户互动和信息粘性，引入一套关注系统。用户（A用户）可以关注其他用户（B用户）或其发布的单条报价。当被关注的对象状态更新时，系统将向关注者发送通知，确保其不会错过重要信息。同时，发布者（B用户）也能看到自己的内容和主页被关注的热度，获得正向反馈。

核心功能点：
- **双重关注机制**：支持用户级关注和报价级关注。
- **实时通知系统**：在内容更新时，向关注者推送提醒。
- **关注度展示**：在发布者侧显示其用户主页和单条报价的被关注数量。
- **个性化关注列表**：为用户提供一个专门页面，管理和浏览所有自己关注的内容。

## 2. 数据定义

### 2.1. 关注关系模型 (Follow)

需要一张新表来存储关注关系。

```typescript
// 关注类型
enum FollowType { 
  USER = 'User', // 关注用户
  QUOTATION = 'Quotation' // 关注单条报价
}

// 关注关系数据模型
interface IFollow {
  id: number;
  followerUserId: number; // 关注者的用户ID (A用户)
  followedUserId?: number; // 被关注的用户ID (B用户)，当 type 为 'User' 时有效
  followedQuotationId?: number; // 被关注的报价ID，当 type 为 'Quotation' 时有效
  type: FollowType; // 关注类型
  createdAt: string; // 创建时间
}
```

### 2.2. 通知模型 (Notification)

需要一张新表来存储通知消息。

```typescript
// 通知数据模型
interface INotification {
  id: number;
  recipientUserId: number; // 接收通知的用户ID (A用户)
  content: string; // 通知内容，例如："您关注的 '电解铜' 报价已更新"
  relatedQuotationId?: number; // 关联的报价ID，用于点击跳转
  isRead: boolean; // 是否已读
  createdAt: string; // 创建时间
}
```

## 3. 子功能详细设计

### 3.1. 关注与取消关注 (A用户视角)

1.  **关注用户**:
    -   **位置**: 在用户的公开报价列表页 (`/pages/quotes/public-list?id=...`) 的顶部用户信息区域。
    -   **控件**: 添加一个“关注”按钮。点击后，按钮状态变为“已关注”，并可再次点击取消关注。

2.  **关注报价**:
    -   **位置**: 在报价详情页 (`/pages/quotes/detail?id=...`) 的显著位置，如标题旁边。
    -   **控件**: 使用一个图标（如“星星”或“爱心”）作为关注按钮。点击后图标变为高亮/实心状态，表示已关注，再次点击则取消。
    -   **重要规则**: 用户不能关注自己发布的报价。因此，当登录用户是该报价的发布者时，此关注按钮应被隐藏或禁用。

### 3.2. 关注度展示 (B用户视角)

1.  **我的报价列表 (`my-list`)**:
    -   **用户关注数**: 在页面顶部，显示“总关注数：XXX”，展示有多少用户关注了你。
    -   **单条报价关注数**: 在每条报价信息的底部，增加一个“被关注：X次”的标签。

2.  **数据获取**: 后端在返回 `my-list` 的数据时，需要在每个报价对象中附加 `followCount` 字段，并额外提供用户的总关注数 `totalFollowerCount`。

### 3.3. 新增页面：我的关注

- **路径**: `pages/my/follows.vue`
- **功能**: 集中展示A用户所有关注的内容。

#### 3.3.1. 页面布局与交互

1.  **顶部Tabs**: 提供两个标签页：“关注的报价”和“关注的用户”。
2.  **“关注的报价” 标签页 (默认)**:
    -   以卡片列表形式展示所有已关注的报价。
    -   **核心逻辑**: 调用API获取列表时，后端必须过滤掉所有**非发布状态**（如草稿、已下架）的报价。
    -   每个卡片上提供“取消关注”的快捷操作。
3.  **“关注的用户” 标签页**:
    -   以列表形式展示所有已关注的用户。
    -   每项包括用户头像、昵称和“取消关注”按钮。

### 3.4. 通知系统

1.  **触发机制 (后端)**:
    -   当B用户**更新一条报价**时，后端检查 `Follow` 表，找出所有关注了该条报价或关注了B用户的A用户列表。
    -   为这些A用户创建 `Notification` 记录。
    -   当B用户**发布一条新报价**时，后端检查 `Follow` 表，找出所有关注了B用户的A用户列表，并为其创建通知。

2.  **前端展示**:
    -   在App的全局导航栏（如 `tabBar` 或顶部导航）中增加一个“通知”或“消息”图标。
    -   使用红点或数字角标显示未读通知数量。
    -   点击图标，跳转到通知列表页面，展示所有收到的通知。

## 4. 接口定义 (API)

### 4.1. `POST /api/v1/follows` (新增)
- **功能**: 创建一个关注关系。
- **请求体**: `{ type: 'User' | 'Quotation', id: number }` (id为用户ID或报价ID)
- **校验逻辑**:
  - **禁止关注自己**: 如果 `type` 是 `Quotation`，后端必须校验请求者（`followerUserId`）是否为该报价的发布者。如果是，则返回错误，不允许创建关注关系。
- **响应**: 成功或失败状态（附带明确的错误信息）。

### 4.2. `DELETE /api/v1/follows` (新增)
- **功能**: 取消一个关注关系。
- **请求体**: `{ type: 'User' | 'Quotation', id: number }`
- **响应**: 成功或失败状态。

### 4.3. `GET /api/v1/my-follows` (新增)
- **功能**: 获取当前用户关注的内容列表。
- **请求参数**: `?type=Quotation` 或 `?type=User`
- **响应 (type=Quotation)**: 返回过滤后的报价列表（仅限已发布）。
- **响应 (type=User)**: 返回关注的用户列表。

### 4.4. `GET /api/v1/quotations/my-list` (修改)
- **功能**: 获取自己发布的报价列表。
- **响应 (修改)**: 在返回的每个报价对象中，增加 `followCount: number` 字段。在顶层数据中，增加 `totalFollowerCount: number` 字段。

### 4.5. `GET /api/v1/notifications` (新增)
- **功能**: 获取当前用户的通知列表。
- **响应**: 返回 `INotification[]` 列表。

### 4.6. `POST /api/v1/notifications/mark-as-read` (新增)
- **功能**: 将一条或多条通知标记为已读。
- **请求体**: `{ ids: number[] }`
- **响应**: 成功或失败状态。

## 5. 相关页面

- `pages/quotes/public-list.vue` (需改造，添加“关注用户”按钮)
- `pages/quotes/detail.vue` (需改造，添加“关注报价”按钮)
- `pages/my/my-list.vue` (需改造，显示关注数)
- `pages/my/follows.vue` (需新建)
- `pages/notification/list.vue` (需新建)
- `layouts/default.vue` (可能需改造，添加全局通知入口)

## 6. 测试用例

1.  **关注/取消**: 在用户页和报价详情页，反复进行关注和取消关注操作，验证状态切换是否正确。
2.  **数据显示**: B用户在 `my-list` 页面能看到正确的总关注数和单条报价关注数。
3.  **关注列表过滤**: A用户在 `my-follows` 页面，当B用户将被关注的报价设为草稿时，该报价应从A的列表中消失。
4.  **通知接收**: B用户更新报价后，A用户应能收到通知。B用户发布新报价，关注了B的A用户应能收到通知。
5.  **禁止关注自己**: 用户在自己的报价详情页，应看不到或无法点击关注按钮。直接调用API尝试关注自己的报价，应返回失败。

## 7. 注意事项

- **后端逻辑是核心**: 该功能的实现严重依赖后端。通知触发、数据关联和过滤逻辑都需要后端完成。
- **通知的实时性**: 初期可以通过轮询 `GET /api/v1/notifications` 接口实现“伪实时”通知。如果需要真·实时，后续可升级为 WebSocket 方案。
- **性能**: 当一个用户被大量关注时，更新一条报价可能会触发大量通知。后端需要考虑使用消息队列等异步任务来处理通知的生成和推送，避免阻塞主流程。
