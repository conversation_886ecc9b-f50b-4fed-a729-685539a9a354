# 功能特性文档：公开报价市场 (Public Quotation Marketplace)

> **版本**: 6.0.0 (Consolidated)
> **负责人**: Gemini
> **状态**: 设计完成

---

## 1. 功能模块简介 (Summary)

本功能旨在创建一个双向、公开、高效的商品报价生态系统。它为报价方提供了一套完整的工具来创建、管理、发布及推广商品报价；同时，为所有市场参与者（包括未登录用户）提供了一个透明的平台来浏览、搜索、发现报价，并通过社交分享和海报功能扩大报价的传播范围，最终为交易撮合奠定基础。

- **报价方工作流**: 用户可以创建报价草稿，进行编辑，发布到公开市场，并通过专属的用户主页和社交分享功能进行推广。
- **市场参与者工作流**: 任何用户都可以访问市场，按条件筛选和搜索报价，查看报价详情、热度指标，访问报价方主页，并将感兴趣的内容生成海报进行分享。

---

## 2. 数据定义 (Data Definition)

### 2.1. 报价表 (`quotations`)

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | PK, AI | 唯一ID |
| `user_id` | `BIGINT` | NOT NULL, FK | 报价发布者的用户ID |
| `title` | `VARCHAR(255)`| NOT NULL | 报价的醒目标题 |
| **`commodity_name`** | `VARCHAR(100)`| NOT NULL | **商品名称 (自由输入)** |
| `delivery_location` | `VARCHAR(255)`| NOT NULL | 交货地点 |
| `brand` | `VARCHAR(100)`| NULL | 品牌 |
| `specifications` | `TEXT` | NULL | 规格说明 |
| `description` | `TEXT` | NULL | 补充说明 |
| `price_type` | `ENUM('Fixed', 'Basis')` | NOT NULL | 报价方式: 'Fixed' (一口价), 'Basis' (基差) |
| `price` | `DECIMAL(10,2)`| NOT NULL | 价格或基差值 |
| `instrument_ref_id` | `BIGINT` | NULL, FK | 关联的期货合约ID (基差报价时使用) |
| `expires_at` | `TIMESTAMP` | NOT NULL | 报价的精确过期时间 |
| **`status`** | `ENUM('Draft', 'Active', 'Expired', 'Withdrawn')` | NOT NULL, DEFAULT 'Draft' | **报价状态** |
| **`view_count`** | `INT` | NOT NULL, DEFAULT 0 | **报价被浏览次数** |
| `created_at` | `TIMESTAMP` | | 创建时间 |
| `updated_at` | `TIMESTAMP` | | 更新时间 |

### 2.2. 报价每日统计表 (`quotation_daily_stats`)

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `stat_date` | `DATE` | PK | 统计日期 |
| `new_quotes_count` | `INT` | NOT NULL | 当日新增报价总数 |
| `total_views_count`| `INT` | NOT NULL | 当日所有报价被浏览总次数 |

### 2.3. 报价状态机 (Quotation State Machine)

```mermaid
graph TD
    Draft -- 用户发布 / Publish --> Active;
    Active -- 用户撤回 / Withdraw --> Withdrawn;
    Active -- 定时任务过期 / Expires --> Expired;
    Draft -- 用户编辑 / Edit --> Draft;
    Draft -- 用户删除 / Delete --> Deleted;
    Withdrawn -- 用户重新发布 / Republish --> Active;
    Expired -- 用户重新发布 / Republish --> Active;
```

---

## 3. 子功能：报价管理 (Quotation Management for Quoters)

### 3.1. 功能设计 (Functional Design)
- **创建/编辑报价**: 提供一个统一的表单页面，用于创建新报价或修改现有草稿。
  - **商品输入**: “商品名称”为自由文本输入框，可带自动补全建议，提高灵活性。
  - **有效期**: 提供便捷选项（如 `当日有效`, `3天内有效`），并计算出最终的 `expires_at` 时间戳。
  - **核心操作**: 用户可选择 `[ 保存草稿 ]` 或 `[ 直接发布 ]`。
- **我的报价列表**: 集中展示和管理当前用户创建的所有报价。
  - **状态过滤**: 可按“有效报价” (`Active`) 和“无效报价” (`Draft`, `Expired`, `Withdrawn`) 进行筛选。
  - **快捷操作**: 列表项提供 `编辑`、`发布`、`撤回`、`删除` 等上下文相关操作。

### 3.2. 相关页面 (Related Pages)
- `app/src/pages/quotes/edit.vue`
- `app/src/pages/quotes/my-list.vue`

---

## 4. 子功能：公开市场与详情 (Public Marketplace & Details)

### 4.1. 功能设计 (Functional Design)

#### 4.1.1. 交互优化：双层动态个性化市场

为解决在大量交易品种（50+）下传统筛选模式效率低下的问题，对公开市场页面的交互进行重构，采用“双层动态个性化”模型，旨在为用户提供兼具个性化、便捷性与扩展性的浏览体验。

**第一层：个性化市场主页 (高频使用)**

此为用户进入报价市场后直接面对的主界面，核心是将通用的“筛选”功能升级为以用户为中心的“品类切换”功能。

- **动态品类标签栏**: 
  - 移除原有的“筛选”按钮，在页面顶部（搜索栏下方）引入一个**横向可滚动的品类标签栏**。
  - 该标签栏的内容根据用户的关注列表动态生成，主要包含三部分：
    1.  **“关注”标签 (固定)**: 作为默认视图，聚合展示用户所有已关注品种的报价列表。
    2.  **已关注品类标签 (动态)**: 遍历用户已关注的品种（如“螺纹钢”、“豆粕”），为每一项生成一个独立的标签。
    3.  **“+ 管理”按钮 (固定)**: 始终位于标签栏末尾，作为进入第二层“品种管理”页面的唯一入口。

- **核心交互流程**:
  - 用户进入页面，默认选中“关注”标签，即时看到自己最关心的报价信息流。
  - 用户可通过左右滑动或直接点击标签，在不同关注品类间无缝切换，极大缩短了信息触达路径。

**第二层：全品类选择与管理 (低频使用)**

这是一个独立的、功能集中的品种管理页面，仅在用户需要调整其关注范围时进入。

- **页面定位**: 一个从主市场页面弹出的全屏或半屏面板，专注于提供高效的品种发现与管理功能。
- **核心布局与交互**:
  1.  **高效查找**: 页面顶部提供一个**搜索框**，支持按品种名称或拼音首字母进行实时模糊搜索。主体部分采用**索引列表（Index List）**组件，将所有可用交易品种按A-Z进行分组，并提供右侧快速索引条，便于在海量品种中快速定位。
  2.  **即时关注/取关**: 列表中的每个品种项均配有一个**星形“关注”按钮**。用户点击该按钮即可立即添加或移除关注，其状态（高亮/灰色）会即时更新，操作直观且无需二次确认。
  3.  **数据持久化**: 用户的关注列表将被持久化存储（本地或服务端），确保用户偏好在不同设备或会话间保持一致。
  4.  **返回与刷新**: 用户完成管理操作并关闭此页面后，市场主页会自动刷新其顶部的动态品类标签栏，以反映最新的关注列表。

### 4.2. 相关页面 (Related Pages)
- `app/src/pages/quotes/marketplace.vue`
- `app/src/pages/quotes/{id}.vue`
- `app/src/pages/quotes/commodity-management.vue` (新增)

---

## 5. 子功能：用户主页与社交推广 (User Homepage & Social Promotion)

### 5.1. 功能设计 (Functional Design)
- **用户报价主页**: 聚合展示特定用户（公司）的公开档案及其所有 `Active` 状态的报价。
  - **所有者快捷入口**: 若当前登录用户访问自己的主页，导航栏将显示“管理我的报价”按钮，快速跳转至 `my-list.vue` 页面。
- **微信小程序分享**:
  - 在“我的报价”页面 (`my-list.vue`) 提供分享入口。
  - 用户可将自己的主页分享给微信好友或群聊。
  - 分享卡片包含动态标题（如“快来看看XXX的最新报价”）和目标路径 `pages/quotes/public-list?id={userId}`。
- **生成分享海报**:
  - **组件化实现**: 海报生成逻辑被封装在独立的 `QuotationListPoster.vue` 组件中，由 `public-list.vue` 页面调用。
  - **一键生成**: 在用户主页，任何访客都可以点击“生成分享海报”按钮。
  - **模板化内容**: 海报内容基于JSON模板动态生成，包含三段式结构：
    1.  **顶部**: 用户头像、企业名称、联系方式等。
    2.  **中部**: 精选的2-3条最新报价。
    3.  **底部**: 动态生成的、指向当前主页的小程序码和引导文案。

### 5.2. 相关页面 (Related Pages)
- `app/src/pages/quotes/public-list.vue` (此页面兼做用户主页)
- `app/src/components/marketplace/QuotationListPoster.vue`

---

## 6. 后端核心逻辑与接口定义 (Backend Logic & API Definition)

### 6.1. 后台任务
- **报价自动过期**: 一个后台定时任务（Cron Job）需稳定运行，定期将 `status = 'Active'` 且 `expires_at < NOW()` 的报价更新为 `Expired` 状态。
- **每日数据统计**: 另一个后台定时任务在每日凌晨运行，聚合计算前一日的数据并写入 `quotation_daily_stats` 表。

### 6.2. 统一接口定义
| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 权限 | 核心参数/Body/说明 |
| :--- | :--- | :--- | :--- | :--- |
| **创建/更新报价** | `POST` / `PUT` | `/api/v1/my/quotations` | 私有 | `Quotation` 对象。创建或更新，状态为`Draft`或`Active` |
| **获取我的报价列表** | `GET` | `/api/v1/my/quotations` | 私有 | `?filter=active/inactive`。获取自己的报价列表 |
| **获取我的报价详情** | `GET` | `/api/v1/my/quotations/{id}` | 私有 | (无) |
| **删除我的报价** | `DELETE` | `/api/v1/my/quotations/{id}` | 私有 | 只能删除`Draft`或`Withdrawn`状态的报价 |
| **获取公开报价列表** | `GET` | `/api/v1/quotations` | 公开 | `?commodity_name=螺纹钢&q=关键词&user_id=123` |
| **获取公开报价详情** | `GET` | `/api/v1/quotations/{id}` | 公开 | (无) |
| **记录一次报价浏览** | `POST` | `/api/v1/quotations/{id}/view` | 公开 | (无) 后端递增 `view_count`，并有防刷机制 |
| **获取市场每日洞察** | `GET` | `/api/v1/quotations/stats` | 公开 | (无) 获取数据看板数据 |
| **获取用户公开档案** | `GET` | `/api/v1/users/{id}/profile` | 公开 | (无) 获取用户主页所需的公司信息 |

---

## 7. 统一测试用例 (Consolidated Test Cases)

| 用例ID | 模块 | 场景描述 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-QT-001` | 报价管理 | 创建一口价草稿，商品名称为“特级初榨橄榄油” | 报价创建成功，`status`为`Draft`，`commodity_name`被正确记录。 |
| `TC-QT-002` | 报价管理 | 用户在“我的报价”中找到草稿并点击“发布” | 报价状态变为`Active`，在市场页可见。 |
| `TC-QT-003` | 报价管理 | 在“我的报价”页选择“无效报价”过滤器 | 列表正确显示`Draft`, `Expired`, `Withdrawn`状态的报价。 |
| `TC-MK-001` | 公开市场 | 在市场页按`螺纹钢`分类并搜索`中粮` | 列表正确显示`中粮`发布的`螺纹钢`有效报价。 |
| `TC-MK-002` | 公开市场 | 多次刷新或访问同一个报价详情页 | `view_count` 正确递增，后台统计任务能正确聚合数据。 |
| `TC-DP-001` | 详情页 | 发布者A访问自己报价的详情页 | 看到“编辑”按钮，不显示自己的公司名。 |
| `TC-DP-002` | 详情页 | 用户B访问A的报价详情页 | 看到A的公司名和“发起点价”按钮，不显示“编辑”按钮。 |
| `TC-UH-001` | 用户主页 | 从市场页点击“中粮集团”的报价 | 成功跳转到中粮集团的用户主页，并显示其所有有效报价。 |
| `TC-SH-001` | 社交推广 | 用户A在“我的报价”页点击分享，并发送给用户B | 用户B收到的卡片标题正确，点击后成功跳转到用户A的主页。 |
| `TC-PO-001` | 社交推广 | 在用户主页点击“生成分享海报” | 成功生成一张包含正确用户信息、报价和可跳转小程序码的图片。 |
| `TC-BE-001` | 后台任务 | 一个`Active`的报价到达`expires_at`时间后 | 被定时任务更新为`Expired`状态，并从市场页消失。 |

---

## 8. 注意事项 (Notes/Caveats)

- **权限控制**: 所有 `/api/v1/my/*` 接口需要用户登录认证。公开接口则无需认证。
- **数据迁移**: 从旧版本升级时，需要编写一次性脚本，将 `commodity_id` 映射并填充到新的 `commodity_name` 字段。
- **系统可靠性**: 处理报价过期和每日统计的后台定时任务必须稳定可靠，否则将影响市场数据的准确性。
- **性能与安全**:
  - `POST /view` 接口需实现基础的防刷逻辑（如IP/用户ID限流）。
  - `GET /users/{id}/profile` 接口必须严格过滤，仅返回允许公开展示的用户信息。
  - 海报生成功能中使用的网络图片（如用户头像）需确保小程序后台已配置相应的域名白名单。
- **下一步集成**: 详情页的“发起点价”按钮是衔接交易流程的关键，需与交易请求模块紧密集成。
