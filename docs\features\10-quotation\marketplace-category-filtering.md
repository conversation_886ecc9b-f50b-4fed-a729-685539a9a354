# 报价市场按商品分类筛选功能升级方案

## 1. 功能简介

当前 `报价市场` 页面会展示所有商品种类的公开报价，对于只关心特定几个种类的用户来说，信息过载严重，筛选效率低下。本次升级旨在优化该页面的使用体验，允许用户自定义关注的商品分类，从而只展示与其最相关的信息，提升信息获取效率和个性化体验。

核心改动：
- **新增“商品分类选择”页面**：提供一个独立的管理界面，让用户可以方便地浏览、搜索和选择自己感兴趣的商品分类。
- **改造“报价市场”页面**：该页面将根据用户选择的分类来精准展示报价列表，并提供引导和修改入口。

## 2. 数据定义

### 2.1. 商品分类模型

为了支持按版块展示，需要定义层级数据结构。

```typescript
// 商品分类
interface ICategory {
  id: number;      // 分类ID
  name: string;    // 分类名称
}

// 商品版块
interface ICategorySection {
  id: number;      // 版块ID
  name: string;    // 版块名称
  categories: ICategory[]; // 该版块下的分类列表
}
```

### 2.2. 用户选择存储

用户的选择将存储在本地缓存中，以便长期记住用户的偏好。

- **Key**: `user_selected_categories`
- **Type**: `number[]` (存储用户选择的 `ICategory` 的ID数组)
- **Example**: `[101, 102, 205]`

## 3. 子功能详细设计

### 3.1. 新增页面：商品分类选择页

- **路径**: `pages/quotes/category-selection.vue`
- **定位**: 用户的个性化“行情订阅中心”。

#### 3.1.1. 页面布局与交互

1.  **导航栏**:
    -   标题：“关注的分类”或“行情订阅”。
    -   右侧：“管理”按钮，点击后进入编辑模式。

2.  **内容区 (常规模式)**:
    -   以列表形式展示用户**已选择**的分类，按版块分组。
    -   每个分类项显示名称，并可拖动排序（优化项）。
    -   如果用户还未选择任何分类，则显示空状态，并引导用户点击“管理”或“添加”来选择分类。

3.  **内容区 (编辑模式)**:
    -   **顶部搜索框**: 方便用户在50多个分类中快速搜索。
    -   **分类树**:
        -   以“版块”为单位折叠或展开列表。
        -   每个分类前有一个复选框（Checkbox），用于勾选。
        -   清晰展示所有可选分类和当前已选中的状态。
    -   **全选/反选**: 每个版块标题旁提供“全选”功能。

4.  **底部操作栏 (编辑模式)**:
    -   一个醒目的“保存”按钮，点击后保存用户的选择到本地缓存，并返回“报价市场”页。

#### 3.1.2. 数据流

1.  **进入页面**:
    -   调用新的 `GET /api/v1/product-categories` 接口，获取全量的“版块-分类”数据。
    -   从本地缓存 `uni.getStorageSync('user_selected_categories')` 读取用户已保存的分类ID列表。
    -   根据读到的ID列表，渲染分类树的勾选状态。

2.  **用户操作**:
    -   用户在分类树上勾选/取消勾选。
    -   点击“保存”。

3.  **保存操作**:
    -   获取所有被勾选的分类ID，组成一个数组。
    -   调用 `uni.setStorageSync('user_selected_categories', selectedIds)` 将新选择的ID数组存入本地。
    -   `uni.navigateBack()` 返回上一页。

### 3.2. 改造页面：报价市场

- **路径**: `app/src/pages/quotes/marketplace.vue`

#### 3.2.1. 页面逻辑改造

1.  **启动/显示时检查 (onLoad / onShow)**:
    -   检查本地缓存中是否存在 `user_selected_categories`。
    -   **若不存在或为空**: 显示一个“欢迎”或“引导”视图，替代当前的列表。内容包括：
        -   友好的提示文案，如：“请先选择您关注的商品，我们将为您精准展示相关报价。”
        -   一个醒目的按钮：“前往选择”，点击后跳转到 `category-selection` 页面。
    -   **若存在**: 读取已选择的分类ID，用于后续的API请求。

2.  **数据请求改造 (`loadQuotationList`)**:
    -   在调用 `getPublicQuotationList` 接口时，在请求参数中加入新的 `categoryIds` 字段。
    -   `params.categoryIds = uni.getStorageSync('user_selected_categories')`

3.  **刷新机制**:
    -   从 `category-selection` 页面返回时，`onShow` 钩子需要重新检查缓存。如果发现选择有变，应自动触发一次列表刷新 `onRefresh()`。

#### 3.2.2. UI/UX 改造

1.  **导航栏/顶部区域**:
    -   在“筛选”按钮旁边，新增一个“管理分类”或类似的图标/文字按钮。
    -   点击该按钮，用户可以随时跳转到 `category-selection` 页面去修改自己的偏好。

2.  **筛选面板**:
    -   当前的筛选（价格类型、关键词）逻辑保持不变，它们将和新的分类筛选共同作用，为用户提供更强大的过滤能力。

## 4. 接口定义 (API)

### 4.1. `GET /api/v1/product-categories` (新增)

- **功能**: 获取所有可供选择的商品分类及其所属版块。
- **请求参数**: 无
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": [
      {
        "id": 1,
        "name": "黑色金属",
        "categories": [
          { "id": 101, "name": "螺纹钢" },
          { "id": 102, "name": "铁矿石" }
        ]
      },
      {
        "id": 2,
        "name": "有色金属",
        "categories": [
          { "id": 201, "name": "电解铜" },
          { "id": 202, "name": "电解铝" }
        ]
      }
    ]
  }
  ```

### 4.2. `GET /api/v1/public-quotations` (修改)

- **功能**: 根据条件获取公开报价列表。
- **请求参数 (新增)**:
  - `categoryIds` (string | number[]): 可选。一个或多个商品分类ID，用逗号分隔的字符串或数组形式。
- **示例**: `/api/v1/public-quotations?page=1&pageSize=10&categoryIds=101,102`

## 5. 相关页面

- `pages/quotes/marketplace.vue` (需改造)
- `pages/quotes/category-selection.vue` (需新建)

## 6. 测试用例

1.  **首次进入**: 用户首次进入市场页，应看到引导选择分类的视图，而不是空的列表。
2.  **分类选择**:
    -   进入分类选择页，能正确显示所有分类，并高亮已选中的项。
    -   搜索功能可正常使用。
    -   保存后，返回市场页，数据能刷新。
3.  **市场页数据展示**:
    -   市场页只展示用户已选分类的报价。
    -   取消所有分类的选择后，市场页应再次显示引导视图。
4.  **组合筛选**:
    -   在已选分类的基础上，关键词搜索和价格类型筛选应能正常工作。

## 7. 注意事项

- **后端依赖**: 此方案强依赖后端提供上述新增和修改的两个API接口。需与后端开发人员提前沟通。
- **用户引导**: 首次使用的引导非常关键，需要做到清晰、无歧义。
- **性能考量**: 如果分类非常多，分类选择页的渲染性能需要注意，可考虑虚拟列表等优化方案（二期优化）。
