# 报价浏览统计功能设计方案

## 1. 功能简介

在“关注”功能的基础上，为报价发布者引入一套浏览量统计系统。此系统旨在追踪并展示其发布的每一条报价被访问的详细情况，包括总浏览量和每日趋势。通过将“关注数”（代表用户粘性）与“浏览量”（代表内容曝光度）相结合，发布者可以全面、清晰地评估自己报价的吸引力，从而优化其发布策略。

核心功能点：
- **自动浏览记录**：用户每次进入报价详情页，系统将自动记录一次有效浏览。
- **智能去重机制**：为保证数据准确性，系统将对同一用户在同一天内的重复访问进行去重处理。
- **数据聚合与展示**：系统会定时聚合原始浏览数据，并在发布者的“我的报价”列表中，直观地展示每条报价的总浏览量和今日浏览量。
- **提供每日趋势分析**：用户可以进一步查看单条报价在过去一段时间（如30天）的每日浏览量图表，洞察其热度变化。

## 2. 数据定义

### 2.1. 浏览日志模型 (QuotationViewLog)

需要一张新表来记录每一次最原始的浏览行为，这是数据分析的基础。

```typescript
// 浏览日志数据模型
interface IQuotationViewLog {
  id: number;
  quotationId: number;    // 被浏览的报价ID
  viewerUserId: number;   // 浏览者的用户ID
  viewedAt: string;       // 浏览发生的精确时间
}
```

### 2.2. 每日数据统计模型 (QuotationDailyStats)

为提高查询效率，避免直接对巨大的日志表进行实时计算，需要一张统计表，由后台定时任务每日更新。

```typescript
// 每日统计数据模型
interface IQuotationDailyStats {
  id: number;
  quotationId: number;    // 报价ID
  date: string;           // 统计日期 (格式: YYYY-MM-DD)
  viewCount: number;      // 当日独立浏览总数 (Unique Views, UV)
}
```

## 3. 子功能详细设计

### 3.1. 浏览行为记录 (前端触发)

- **触发点**: 在报价详情页 (`/pages/quotes/detail.vue`) 的 `onMounted` 或 `onLoad`生命周期钩子中。
- **执行逻辑**: 调用一个新的API接口，将当前报价的ID发送给后端。
- **前端优化**: 为避免重复触发，可以在页面实例中设置一个标志位，确保在单次页面生命周期内只上报一次。

### 3.2. 数据接收与去重 (后端处理)

- **核心逻辑**: 后端接收到前端的浏览记录请求后，**不是立即增加计数值**，而是先写入 `QuotationViewLog` 表。
- **去重机制**: 在后续的数据聚合阶段实现。这种方式比在接收时实时检查更高效，可以快速响应前端请求。

### 3.3. 数据聚合 (后端定时任务)

- **触发方式**: 一个每日执行的定时任务（如 Cron Job），建议在服务器负载较低的凌晨（如 00:30）执行。
- **任务内容**:
  1.  扫描前一天的 `QuotationViewLog` 表。
  2.  按 `quotationId` 和 `viewerUserId` 对日志进行分组和去重，统计出每条报价在前一天的**独立用户浏览量 (UV)**。
  3.  将计算结果写入 `QuotationDailyStats` 表中。
  4.  （可选）为了节省存储空间，可以归档或删除已经处理过的、过老的 `QuotationViewLog` 记录。

### 3.4. 统计数据展示 (发布者视角)

- **位置**: 在“我的报价”列表页 (`/pages/my/my-list.vue`)。
- **UI改造**:
  1.  在每条报价卡片上，于“被关注数”旁边，新增“总浏览”和“今日浏览”两个数据标签。
  2.  为浏览数据增加一个可点击的图标（如“趋势图”图标）。点击后，弹出一个模态框（Modal）或跳转到新页面。
- **模态框/新页面内容**:
  -   显示一个简单的线性图表，展示该报价在过去30天的每日浏览量变化趋势。

## 4. 接口定义 (API)

### 4.1. `POST /api/v1/quotations/{id}/record-view` (新增)
- **功能**: 记录一次报价浏览行为。
- **URL参数**: `id` - 报价的ID。
- **请求体**: 无。
- **响应**: 快速响应成功状态 (e.g., `202 Accepted`)，表示请求已被接收，将异步处理。

### 4.2. `GET /api/v1/quotations/my-list` (修改)
- **功能**: 获取自己发布的报价列表。
- **响应 (修改)**: 在返回的每个报价对象中，除了已有的 `followCount`，再增加两个字段：
  - `totalViewCount: number` (从 `QuotationDailyStats` 表中累加得出)
  - `todayViewCount: number` (从 `QuotationDailyStats` 表中查询今日数据得出)

### 4.3. `GET /api/v1/quotations/{id}/stats` (新增)
- **功能**: 获取单条报价的详细统计数据，用于图表展示。
- **URL参数**: `id` - 报价的ID。
- **请求参数**: `?days=30` (可选，默认为30天)
- **响应**: 返回一个包含日期和计数的数组。
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": [
      { "date": "2025-07-07", "viewCount": 15 },
      { "date": "2025-07-08", "viewCount": 28 },
      ...
    ]
  }
  ```

## 5. 相关页面

- `pages/quotes/detail.vue` (需改造，在 `onMounted` 中调用记录API)
- `pages/my/my-list.vue` (需改造，展示统计数据和图表入口)

## 6. 测试用例

1.  **浏览记录**: 访问报价详情页，验证 `QuotationViewLog` 表中是否新增了对应记录。
2.  **浏览去重**: 同一用户在一天内多次访问同一报价详情页，`QuotationViewLog` 表会新增多条记录，但第二天的 `QuotationDailyStats` 表中，该报价的计数值只应增加1。
3.  **数据展示**: 在 `my-list` 页面，验证“总浏览”和“今日浏览”的数字是否与后端统计一致。
4.  **图表功能**: 点击统计图表入口，能正确弹出图表并显示最近30天的数据。

## 7. 注意事项

- **后端定时任务是关键**: 此功能的核心在于后端稳定、准确的每日数据聚合任务。
- **数据准确性**: 明确向用户传达，“浏览量”是“独立用户浏览量(UV)”，避免因定义不清产生误解。
- **性能考量**: `QuotationViewLog` 表可能会快速增长。后端的聚合与归档策略对于维持系统长期性能至关重要。
